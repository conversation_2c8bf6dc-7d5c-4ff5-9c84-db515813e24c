<?php

namespace App\Services;

use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class ExcelHelper
{
	protected $filename;

	public function __construct()
	{
		$this->filename = 'exported_excel_' . time() . '.xls';
	}

	public function readExcel($file)
	{
		$reader = new Xlsx();
		$excel = $reader->load($file);
		$sheet = $excel->getActiveSheet();

		$highestRow = $sheet->getHighestRow();
		$highestColumn = $sheet->getHighestColumn();
		$highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

		$header = [];
		for ($colIndex = 1; $colIndex <= $highestColumnIndex; $colIndex++) {
			$col = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex);
			$header[$col] = Str::slug($sheet->getCell($col . "1")->getValue());
		}

		$data = [];
		for ($row = 2; $row <= $highestRow; $row++) {
			for ($colIndex = 1; $colIndex <= $highestColumnIndex; $colIndex++) {
				$col = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex);
				if (!empty($header[$col])) {
					if (!empty($val = $sheet->getCell($col . $row)->getValue())) {
						$data[$row][$header[$col]] = $val;
					}
				}
			}
		}
		return array_values($data);
	}

	public function createExcel(...$lists)
	{
		$spreadsheet = new Spreadsheet();
		$spreadsheet->removeSheetByIndex(0);
		foreach ($lists as $i => $list) {
			$sheetName = $list['title'] ?? "";
			$options = $list['options'] ?? [];
			$items = $list['data'] ?? [];

			$spreadsheet->createSheet();
			$spreadsheet->setActiveSheetIndex($i)->setTitle($sheetName);

			$header = !empty($items) ? array_keys(reset($items)) : [];
			for ($h = 0; $h <= count($header); $h++) {
				$spreadsheet->getActiveSheet()->setCellValueByColumnAndRow($h + 1, 1, $header[$h] ?? null);
			}

			foreach ($items as $j => $item) {
				$item = array_values($item);
				foreach ($item as $k => $v) {
					$spreadsheet->getActiveSheet()->setCellValueByColumnAndRow($k + 1, $j + 2, $v);
				}
			}

			foreach (range('A', 'Z') as $columnID) {
				$spreadsheet->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
				$spreadsheet->getActiveSheet()->getColumnDimension('A' . $columnID)->setAutoSize(true);
				$spreadsheet->getActiveSheet()->getColumnDimension('B' . $columnID)->setAutoSize(true);
				$spreadsheet->getActiveSheet()->getColumnDimension('C' . $columnID)->setAutoSize(true);
			}

			foreach ($options as $column => $option) {
				switch (strtoupper($option)) {
					case "TEXT":
						$spreadsheet->getActiveSheet()->getStyle($column)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);
						break;
					case "DATE":
						$spreadsheet->getActiveSheet()->getStyle($column)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_DATE_DATETIME);
						break;
					case "TIME":
						$spreadsheet->getActiveSheet()->getStyle($column)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_DATE_TIME4);
						break;
					default:
						break;
				}
			}
		}
		$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
		header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		$headerContent = 'Content-Disposition: attachment;filename="' . $this->filename . '"';
		header($headerContent);
		return $writer->save('php://output');
	}

	public function setFilename($filename)
	{
		$this->filename = $filename . '.xlsx';
	}
}
