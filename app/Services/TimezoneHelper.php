<?php

namespace App\Services;

use DateTimeZone;

class TimezoneHelper
{
    public function list()
    {
        return [
            "Africa/Abidjan" => "Africa/Abidjan (GMT)",
            "Africa/Accra" => "Africa/Accra (GMT)",
            "Africa/Addis_Ababa" => "Africa/Addis_Ababa (EAT)",
            "Africa/Algiers" => "Africa/Algiers (CET)",
            "Africa/Asmara" => "Africa/Asmara (EAT)",
            "Africa/Bamako" => "Africa/Bamako (GMT)",
            "Africa/Bangui" => "Africa/Bangui (WAT)",
            "Africa/Banjul" => "Africa/Banjul (GMT)",
            "Africa/Bissau" => "Africa/Bissau (GMT)",
            "Africa/Blantyre" => "Africa/Blantyre (CAT)",
            "Africa/Brazzaville" => "Africa/Brazzaville (WAT)",
            "Africa/Bujumbura" => "Africa/Bujumbura (CAT)",
            "Asia/Oral" => "Asia/Oral (ORAT)",
            "Africa/Cairo" => "Africa/Cairo (EET)",
            "Africa/Casablanca" => "Africa/Casablanca (WET)",
            "Africa/Ceuta" => "Africa/Ceuta (CET)",
            "Africa/Conakry" => "Africa/Conakry (GMT)",
            "Africa/Dakar" => "Africa/Dakar (GMT)",
            "Africa/Dar_es_Salaam" => "Africa/Dar_es_Salaam (EAT)",
            "Asia/Yekaterinburg" => "Asia/Yekaterinburg (YEKT)",
            "Africa/Djibouti" => "Africa/Djibouti (EAT)",
            "Africa/Douala" => "Africa/Douala (WAT)",
            "Africa/Freetown" => "Africa/Freetown (GMT)",
            "Africa/Gaborone" => "Africa/Gaborone (CAT)",
            "Africa/Harare" => "Africa/Harare (CAT)",
            "Africa/El_Aaiun" => "Africa/El_Aaiun (WET)",
            "Africa/Johannesburg" => "Africa/Johannesburg (SAST)",
            "Africa/Juba" => "Africa/Juba (EAT)",
            "Africa/Kampala" => "Africa/Kampala (EAT)",
            "Africa/Khartoum" => "Africa/Khartoum (EAT)",
            "Africa/Kinshasa" => "Africa/Kinshasa (WAT)",
            "Africa/Lagos" => "Africa/Lagos (WAT)",
            "Africa/Libreville" => "Africa/Libreville (WAT)",
            "Africa/Lome" => "Africa/Lome (GMT)",
            "Africa/Kigali" => "Africa/Kigali (CAT)",
            "Africa/Luanda" => "Africa/Luanda (WAT)",
            "Africa/Lubumbashi" => "Africa/Lubumbashi (CAT)",
            "Africa/Lusaka" => "Africa/Lusaka (CAT)",
            "Africa/Malabo" => "Africa/Malabo (WAT)",
            "Africa/Maputo" => "Africa/Maputo (CAT)",
            "Africa/Mbabane" => "Africa/Mbabane (SAST)",
            "Africa/Mogadishu" => "Africa/Mogadishu (EAT)",
            "Africa/Monrovia" => "Africa/Monrovia (GMT)",
            "Africa/Nairobi" => "Africa/Nairobi (EAT)",
            "Africa/Maseru" => "Africa/Maseru (SAST)",
            "Africa/Ndjamena" => "Africa/Ndjamena (WAT)",
            "Africa/Niamey" => "Africa/Niamey (WAT)",
            "Africa/Nouakchott" => "Africa/Nouakchott (GMT)",
            "Africa/Ouagadougou" => "Africa/Ouagadougou (GMT)",
            "Africa/Porto-Novo" => "Africa/Porto-Novo (WAT)",
            "Africa/Tunis" => "Africa/Tunis (CET)",
            "Africa/Sao_Tome" => "Africa/Sao_Tome (GMT)",
            "Africa/Tripoli" => "Africa/Tripoli (EET)",
            "Africa/Windhoek" => "Africa/Windhoek (WAST)",
            "America/Adak" => "America/Adak (HST)",
            "America/Argentina/Salta" => "America/Argentina/Salta (ART)",
            "America/Anchorage" => "America/Anchorage (AKST)",
            "America/Anguilla" => "America/Anguilla (AST)",
            "America/Antigua" => "America/Antigua (AST)",
            "America/Araguaina" => "America/Araguaina (BRT)",
            "America/Argentina/Buenos_Aires" => "America/Argentina/Buenos_Aires (ART)",
            "America/Argentina/Catamarca" => "America/Argentina/Catamarca (ART)",
            "America/Argentina/Cordoba" => "America/Argentina/Cordoba (ART)",
            "America/Argentina/Jujuy" => "America/Argentina/Jujuy (ART)",
            "America/Argentina/La_Rioja" => "America/Argentina/La_Rioja (ART)",
            "America/Argentina/Mendoza" => "America/Argentina/Mendoza (ART)",
            "America/Argentina/Rio_Gallegos" => "America/Argentina/Rio_Gallegos (ART)",
            "America/Argentina/San_Juan" => "America/Argentina/San_Juan (ART)",
            "America/Argentina/San_Luis" => "America/Argentina/San_Luis (ART)",
            "America/Argentina/Tucuman" => "America/Argentina/Tucuman (ART)",
            "America/Aruba" => "America/Aruba (AST)",
            "America/Argentina/Ushuaia" => "America/Argentina/Ushuaia (ART)",
            "America/Asuncion" => "America/Asuncion (PYST)",
            "America/Bahia_Banderas" => "America/Bahia_Banderas (CST)",
            "America/Atikokan" => "America/Atikokan (EST)",
            "America/Bahia" => "America/Bahia (BRT)",
            "America/Barbados" => "America/Barbados (AST)",
            "America/Belem" => "America/Belem (BRT)",
            "America/Belize" => "America/Belize (CST)",
            "America/Blanc-Sablon" => "America/Blanc-Sablon (AST)",
            "America/Boa_Vista" => "America/Boa_Vista (AMT)",
            "America/Bogota" => "America/Bogota (COT)",
            "America/Boise" => "America/Boise (MST)",
            "America/Cambridge_Bay" => "America/Cambridge_Bay (MST)",
            "America/Campo_Grande" => "America/Campo_Grande (AMST)",
            "America/Cancun" => "America/Cancun (EST)",
            "America/Caracas" => "America/Caracas (VET)",
            "America/Cayenne" => "America/Cayenne (GFT)",
            "America/Cayman" => "America/Cayman (EST)",
            "America/Chicago" => "America/Chicago (CST)",
            "America/Chihuahua" => "America/Chihuahua (MST)",
            "America/Coral_Harbour" => "America/Coral_Harbour (EST)",
            "America/Costa_Rica" => "America/Costa_Rica (CST)",
            "America/Creston" => "America/Creston (MST)",
            "America/Cuiaba" => "America/Cuiaba (AMST)",
            "America/Curacao" => "America/Curacao (AST)",
            "America/Danmarkshavn" => "America/Danmarkshavn (GMT)",
            "America/Dawson" => "America/Dawson (PST)",
            "America/Dawson_Creek" => "America/Dawson_Creek (MST)",
            "America/Denver" => "America/Denver (MST)",
            "America/Detroit" => "America/Detroit (EST)",
            "America/Dominica" => "America/Dominica (AST)",
            "America/Edmonton" => "America/Edmonton (MST)",
            "America/Eirunepe" => "America/Eirunepe (ACT)",
            "America/El_Salvador" => "America/El_Salvador (CST)",
            "America/Fortaleza" => "America/Fortaleza (BRT)",
            "America/Glace_Bay" => "America/Glace_Bay (AST)",
            "America/Godthab" => "America/Godthab (WGT)",
            "America/Goose_Bay" => "America/Goose_Bay (AST)",
            "America/Grand_Turk" => "America/Grand_Turk (AST)",
            "America/Grenada" => "America/Grenada (AST)",
            "America/Guadeloupe" => "America/Guadeloupe (AST)",
            "America/Guatemala" => "America/Guatemala (CST)",
            "America/Guayaquil" => "America/Guayaquil (ECT)",
            "America/Guyana" => "America/Guyana (GYT)",
            "America/Halifax" => "America/Halifax (AST)",
            "America/Havana" => "America/Havana (CST)",
            "America/Hermosillo" => "America/Hermosillo (MST)",
            "America/Indiana/Petersburg" => "America/Indiana/Petersburg (EST)",
            "America/Indiana/Tell_City" => "America/Indiana/Tell_City (CST)",
            "America/Indiana/Vevay" => "America/Indiana/Vevay (EST)",
            "America/Indiana/Indianapolis" => "America/Indiana/Indianapolis (EST)",
            "America/Indiana/Knox" => "America/Indiana/Knox (CST)",
            "America/Indiana/Marengo" => "America/Indiana/Marengo (EST)",
            "America/Indiana/Vincennes" => "America/Indiana/Vincennes (EST)",
            "America/Indiana/Winamac" => "America/Indiana/Winamac (EST)",
            "America/Inuvik" => "America/Inuvik (MST)",
            "America/Iqaluit" => "America/Iqaluit (EST)",
            "America/La_Paz" => "America/La_Paz (BOT)",
            "America/Jamaica" => "America/Jamaica (EST)",
            "America/Juneau" => "America/Juneau (AKST)",
            "America/Kentucky/Louisville" => "America/Kentucky/Louisville (EST)",
            "America/Kentucky/Monticello" => "America/Kentucky/Monticello (EST)",
            "America/Kralendijk" => "America/Kralendijk (AST)",
            "America/Lima" => "America/Lima (PET)",
            "America/Managua" => "America/Managua (CST)",
            "America/Manaus" => "America/Manaus (AMT)",
            "America/Los_Angeles" => "America/Los_Angeles (PST)",
            "America/Lower_Princes" => "America/Lower_Princes (AST)",
            "America/Maceio" => "America/Maceio (BRT)",
            "America/Marigot" => "America/Marigot (AST)",
            "America/Martinique" => "America/Martinique (AST)",
            "America/Matamoros" => "America/Matamoros (CST)",
            "America/Mazatlan" => "America/Mazatlan (MST)",
            "America/Menominee" => "America/Menominee (CST)",
            "America/Mexico_City" => "America/Mexico_City (CST)",
            "America/Merida" => "America/Merida (CST)",
            "America/Metlakatla" => "America/Metlakatla (PST)",
            "America/Miquelon" => "America/Miquelon (PMST)",
            "America/Moncton" => "America/Moncton (AST)",
            "America/Monterrey" => "America/Monterrey (CST)",
            "America/Montevideo" => "America/Montevideo (UYT)",
            "America/Montreal" => "America/Montreal (EST)",
            "America/Montserrat" => "America/Montserrat (AST)",
            "America/Nassau" => "America/Nassau (EST)",
            "America/New_York" => "America/New_York (EST)",
            "America/Nipigon" => "America/Nipigon (EST)",
            "America/Nome" => "America/Nome (AKST)",
            "America/Noronha" => "America/Noronha (FNT)",
            "America/North_Dakota/Beulah" => "America/North_Dakota/Beulah (CST)",
            "America/North_Dakota/Center" => "America/North_Dakota/Center (CST)",
            "America/North_Dakota/New_Salem" => "America/North_Dakota/New_Salem (CST)",
            "America/Ojinaga" => "America/Ojinaga (MST)",
            "America/Panama" => "America/Panama (EST)",
            "America/Pangnirtung" => "America/Pangnirtung (EST)",
            "America/Paramaribo" => "America/Paramaribo (SRT)",
            "America/Phoenix" => "America/Phoenix (MST)",
            "America/Port-au-Prince" => "America/Port-au-Prince (EST)",
            "America/Port_of_Spain" => "America/Port_of_Spain (AST)",
            "America/Porto_Velho" => "America/Porto_Velho (AMT)",
            "America/Puerto_Rico" => "America/Puerto_Rico (AST)",
            "America/Rainy_River" => "America/Rainy_River (CST)",
            "America/Rankin_Inlet" => "America/Rankin_Inlet (CST)",
            "America/Recife" => "America/Recife (BRT)",
            "America/Regina" => "America/Regina (CST)",
            "America/Resolute" => "America/Resolute (CST)",
            "America/Rio_Branco" => "America/Rio_Branco (ACT)",
            "America/Santa_Isabel" => "America/Santa_Isabel (PST)",
            "America/Santarem" => "America/Santarem (BRT)",
            "America/Santiago" => "America/Santiago (CLT)",
            "America/Santo_Domingo" => "America/Santo_Domingo (AST)",
            "America/Scoresbysund" => "America/Scoresbysund (EGT)",
            "America/Sao_Paulo" => "America/Sao_Paulo (BRST)",
            "America/Sitka" => "America/Sitka (AKST)",
            "America/St_Barthelemy" => "America/St_Barthelemy (AST)",
            "America/St_Johns" => "America/St_Johns (NST)",
            "America/Thule" => "America/Thule (AST)",
            "America/St_Kitts" => "America/St_Kitts (AST)",
            "America/St_Lucia" => "America/St_Lucia (AST)",
            "America/St_Thomas" => "America/St_Thomas (AST)",
            "America/St_Vincent" => "America/St_Vincent (AST)",
            "America/Swift_Current" => "America/Swift_Current (CST)",
            "America/Tegucigalpa" => "America/Tegucigalpa (CST)",
            "America/Thunder_Bay" => "America/Thunder_Bay (EST)",
            "America/Tijuana" => "America/Tijuana (PST)",
            "America/Toronto" => "America/Toronto (EST)",
            "America/Tortola" => "America/Tortola (AST)",
            "America/Vancouver" => "America/Vancouver (PST)",
            "America/Whitehorse" => "America/Whitehorse (PST)",
            "America/Winnipeg" => "America/Winnipeg (CST)",
            "America/Yakutat" => "America/Yakutat (AKST)",
            "America/Yellowknife" => "America/Yellowknife (MST)",
            "Antarctica/Macquarie" => "Antarctica/Macquarie (MIST)",
            "Arctic/Longyearbyen" => "Arctic/Longyearbyen (CET)",
            "Asia/Aden" => "Asia/Aden (AST)",
            "Asia/Almaty" => "Asia/Almaty (ALMT)",
            "Asia/Amman" => "Asia/Amman (EET)",
            "Asia/Anadyr" => "Asia/Anadyr (ANAT)",
            "Asia/Aqtau" => "Asia/Aqtau (AQTT)",
            "Asia/Aqtobe" => "Asia/Aqtobe (AQTT)",
            "Asia/Ashgabat" => "Asia/Ashgabat (TMT)",
            "Asia/Baghdad" => "Asia/Baghdad (AST)",
            "Asia/Bahrain" => "Asia/Bahrain (AST)",
            "Asia/Bangkok" => "Asia/Bangkok (ICT)",
            "Asia/Baku" => "Asia/Baku (AZT)",
            "Asia/Chongqing" => "Asia/Chongqing (CST)",
            "Asia/Beirut" => "Asia/Beirut (EET)",
            "Asia/Bishkek" => "Asia/Bishkek (KGT)",
            "Asia/Brunei" => "Asia/Brunei (BNT)",
            "Asia/Choibalsan" => "Asia/Choibalsan (CHOT)",
            "Asia/Colombo" => "Asia/Colombo (IST)",
            "Asia/Damascus" => "Asia/Damascus (EET)",
            "Asia/Dhaka" => "Asia/Dhaka (BDT)",
            "Asia/Dili" => "Asia/Dili (TLT)",
            "Asia/Dubai" => "Asia/Dubai (GST)",
            "Asia/Dushanbe" => "Asia/Dushanbe (TJT)",
            "Asia/Gaza" => "Asia/Gaza (EET)",
            "Asia/Harbin" => "Asia/Harbin (CST)",
            "Asia/Hebron" => "Asia/Hebron (EET)",
            "Asia/Ho_Chi_Minh" => "Asia/Ho_Chi_Minh (ICT)",
            "Asia/Hong_Kong" => "Asia/Hong_Kong (HKT)",
            "Asia/Hovd" => "Asia/Hovd (HOVT)",
            "Asia/Irkutsk" => "Asia/Irkutsk (IRKT)",
            "Asia/Jakarta" => "Asia/Jakarta (WIB)",
            "Asia/Jayapura" => "Asia/Jayapura (WIT)",
            "Asia/Kabul" => "Asia/Kabul (AFT)",
            "Asia/Jerusalem" => "Asia/Jerusalem (IST)",
            "Asia/Kamchatka" => "Asia/Kamchatka (PETT)",
            "Asia/Karachi" => "Asia/Karachi (PKT)",
            "Asia/Kashgar" => "Asia/Kashgar (XJT)",
            "Asia/Kathmandu" => "Asia/Kathmandu (NPT)",
            "Asia/Calcutta" => "Asia/Calcutta (IST)",
            "Asia/Krasnoyarsk" => "Asia/Krasnoyarsk (KRAT)",
            "Asia/Kuala_Lumpur" => "Asia/Kuala_Lumpur (MYT)",
            "Asia/Kuching" => "Asia/Kuching (MYT)",
            "Asia/Kuwait" => "Asia/Kuwait (AST)",
            "Asia/Macau" => "Asia/Macau (CST)",
            "Asia/Magadan" => "Asia/Magadan (MAGT)",
            "Asia/Makassar" => "Asia/Makassar (WITA)",
            "Asia/Manila" => "Asia/Manila (PHT)",
            "Asia/Muscat" => "Asia/Muscat (GST)",
            "Asia/Nicosia" => "Asia/Nicosia (EET)",
            "Asia/Novokuznetsk" => "Asia/Novokuznetsk (KRAT)",
            "Asia/Novosibirsk" => "Asia/Novosibirsk (NOVT)",
            "Asia/Omsk" => "Asia/Omsk (OMST)",
            "Asia/Phnom_Penh" => "Asia/Phnom_Penh (ICT)",
            "Asia/Pontianak" => "Asia/Pontianak (WIB)",
            "Asia/Pyongyang" => "Asia/Pyongyang (KST)",
            "Asia/Qatar" => "Asia/Qatar (AST)",
            "Asia/Qyzylorda" => "Asia/Qyzylorda (QYZT)",
            "Asia/Rangoon" => "Asia/Rangoon (MMT)",
            "Asia/Riyadh" => "Asia/Riyadh (AST)",
            "Asia/Sakhalin" => "Asia/Sakhalin (SAKT)",
            "Asia/Samarkand" => "Asia/Samarkand (UZT)",
            "Asia/Seoul" => "Asia/Seoul (KST)",
            "Asia/Shanghai" => "Asia/Shanghai (CST)",
            "Asia/Singapore" => "Asia/Singapore (SGT)",
            "Asia/Taipei" => "Asia/Taipei (CST)",
            "Asia/Tashkent" => "Asia/Tashkent (UZT)",
            "Asia/Tbilisi" => "Asia/Tbilisi (GET)",
            "Asia/Tehran" => "Asia/Tehran (IRST)",
            "Asia/Thimphu" => "Asia/Thimphu (BTT)",
            "Asia/Tokyo" => "Asia/Tokyo (JST)",
            "Asia/Ulaanbaatar" => "Asia/Ulaanbaatar (ULAT)",
            "Asia/Urumqi" => "Asia/Urumqi (XJT)",
            "Asia/Vientiane" => "Asia/Vientiane (ICT)",
            "Asia/Vladivostok" => "Asia/Vladivostok (VLAT)",
            "Asia/Yakutsk" => "Asia/Yakutsk (YAKT)",
            "Asia/Yerevan" => "Asia/Yerevan (AMT)",
            "Atlantic/Azores" => "Atlantic/Azores (AZOT)",
            "Atlantic/Bermuda" => "Atlantic/Bermuda (AST)",
            "Atlantic/Canary" => "Atlantic/Canary (WET)",
            "Atlantic/Cape_Verde" => "Atlantic/Cape_Verde (CVT)",
            "Atlantic/Faroe" => "Atlantic/Faroe (WET)",
            "Atlantic/Madeira" => "Atlantic/Madeira (WET)",
            "Atlantic/Reykjavik" => "Atlantic/Reykjavik (GMT)",
            "Atlantic/South_Georgia" => "Atlantic/South_Georgia (GST)",
            "Atlantic/St_Helena" => "Atlantic/St_Helena (GMT)",
            "Atlantic/Stanley" => "Atlantic/Stanley (FKST)",
            "Australia/Adelaide" => "Australia/Adelaide (ACDT)",
            "Australia/Brisbane" => "Australia/Brisbane (AEST)",
            "Australia/Broken_Hill" => "Australia/Broken_Hill (ACDT)",
            "Australia/Currie" => "Australia/Currie (AEDT)",
            "Australia/Darwin" => "Australia/Darwin (ACST)",
            "Australia/Eucla" => "Australia/Eucla (ACWST)",
            "Australia/Hobart" => "Australia/Hobart (AEDT)",
            "Australia/Lindeman" => "Australia/Lindeman (AEST)",
            "Australia/Lord_Howe" => "Australia/Lord_Howe (LHDT)",
            "Australia/Melbourne" => "Australia/Melbourne (AEDT)",
            "Australia/Perth" => "Australia/Perth (AWST)",
            "Australia/Sydney" => "Australia/Sydney (AEDT)",
            "Europe/Amsterdam" => "Europe/Amsterdam (CET)",
            "Europe/Andorra" => "Europe/Andorra (CET)",
            "Europe/Athens" => "Europe/Athens (EET)",
            "Europe/Belgrade" => "Europe/Belgrade (CET)",
            "Europe/Bucharest" => "Europe/Bucharest (EET)",
            "Europe/Berlin" => "Europe/Berlin (CET)",
            "Europe/Budapest" => "Europe/Budapest (CET)",
            "Europe/Chisinau" => "Europe/Chisinau (EET)",
            "Europe/Bratislava" => "Europe/Bratislava (CET)",
            "Europe/Brussels" => "Europe/Brussels (CET)",
            "Europe/Copenhagen" => "Europe/Copenhagen (CET)",
            "Europe/Dublin" => "Europe/Dublin (GMT)",
            "Europe/Gibraltar" => "Europe/Gibraltar (CET)",
            "Europe/Guernsey" => "Europe/Guernsey (GMT)",
            "Europe/Helsinki" => "Europe/Helsinki (EET)",
            "Europe/Isle_of_Man" => "Europe/Isle_of_Man (GMT)",
            "Europe/Istanbul" => "Europe/Istanbul (EET)",
            "Europe/Jersey" => "Europe/Jersey (GMT)",
            "Europe/Kaliningrad" => "Europe/Kaliningrad (EET)",
            "Europe/Kiev" => "Europe/Kiev (EET)",
            "Europe/Lisbon" => "Europe/Lisbon (WET)",
            "Europe/Ljubljana" => "Europe/Ljubljana (CET)",
            "Europe/London" => "Europe/London (GMT)",
            "Europe/Luxembourg" => "Europe/Luxembourg (CET)",
            "Europe/Madrid" => "Europe/Madrid (CET)",
            "Europe/Malta" => "Europe/Malta (CET)",
            "Europe/Mariehamn" => "Europe/Mariehamn (EET)",
            "Europe/Minsk" => "Europe/Minsk (MSK)",
            "Europe/Monaco" => "Europe/Monaco (CET)",
            "Europe/Moscow" => "Europe/Moscow (MSK)",
            "Europe/Oslo" => "Europe/Oslo (CET)",
            "Europe/Paris" => "Europe/Paris (CET)",
            "Europe/Podgorica" => "Europe/Podgorica (CET)",
            "Europe/Prague" => "Europe/Prague (CET)",
            "Europe/Riga" => "Europe/Riga (EET)",
            "Europe/Rome" => "Europe/Rome (CET)",
            "Europe/Samara" => "Europe/Samara (SAMT)",
            "Europe/San_Marino" => "Europe/San_Marino (CET)",
            "Europe/Sarajevo" => "Europe/Sarajevo (CET)",
            "Europe/Simferopol" => "Europe/Simferopol (MSK)",
            "Europe/Skopje" => "Europe/Skopje (CET)",
            "Europe/Sofia" => "Europe/Sofia (EET)",
            "Europe/Stockholm" => "Europe/Stockholm (CET)",
            "Europe/Tallinn" => "Europe/Tallinn (EET)",
            "Europe/Tirane" => "Europe/Tirane (CET)",
            "Europe/Uzhgorod" => "Europe/Uzhgorod (EET)",
            "Europe/Vaduz" => "Europe/Vaduz (CET)",
            "Europe/Vatican" => "Europe/Vatican (CET)",
            "Europe/Vienna" => "Europe/Vienna (CET)",
            "Europe/Vilnius" => "Europe/Vilnius (EET)",
            "Europe/Volgograd" => "Europe/Volgograd (MSK)",
            "Europe/Warsaw" => "Europe/Warsaw (CET)",
            "Europe/Zagreb" => "Europe/Zagreb (CET)",
            "Europe/Zaporozhye" => "Europe/Zaporozhye (EET)",
            "Europe/Zurich" => "Europe/Zurich (CET)",
            "Indian/Antananarivo" => "Indian/Antananarivo (EAT)",
            "Indian/Chagos" => "Indian/Chagos (IOT)",
            "Indian/Christmas" => "Indian/Christmas (CXT)",
            "Indian/Cocos" => "Indian/Cocos (CCT)",
            "Indian/Comoro" => "Indian/Comoro (EAT)",
            "Indian/Kerguelen" => "Indian/Kerguelen (TFT)",
            "Indian/Mahe" => "Indian/Mahe (SCT)",
            "Indian/Maldives" => "Indian/Maldives (MVT)",
            "Indian/Mauritius" => "Indian/Mauritius (MUT)",
            "Indian/Mayotte" => "Indian/Mayotte (EAT)",
            "Indian/Reunion" => "Indian/Reunion (RET)",
            "Pacific/Apia" => "Pacific/Apia (WSDT)",
            "Pacific/Auckland" => "Pacific/Auckland (NZDT)",
            "Pacific/Chatham" => "Pacific/Chatham (CHADT)",
            "Pacific/Chuuk" => "Pacific/Chuuk (CHUT)",
            "Pacific/Easter" => "Pacific/Easter (EAST)",
            "Pacific/Enderbury" => "Pacific/Enderbury (PHOT)",
            "Pacific/Fakaofo" => "Pacific/Fakaofo (TKT)",
            "Pacific/Efate" => "Pacific/Efate (VUT)",
            "Pacific/Fiji" => "Pacific/Fiji (FJST)",
            "Pacific/Funafuti" => "Pacific/Funafuti (TVT)",
            "Pacific/Galapagos" => "Pacific/Galapagos (GALT)",
            "Pacific/Gambier" => "Pacific/Gambier (GAMT)",
            "Pacific/Kwajalein" => "Pacific/Kwajalein (MHT)",
            "Pacific/Guadalcanal" => "Pacific/Guadalcanal (SBT)",
            "Pacific/Guam" => "Pacific/Guam (ChST)",
            "Pacific/Honolulu" => "Pacific/Honolulu (HST)",
            "Pacific/Johnston" => "Pacific/Johnston (HST)",
            "Pacific/Kiritimati" => "Pacific/Kiritimati (LINT)",
            "Pacific/Kosrae" => "Pacific/Kosrae (KOST)",
            "Pacific/Majuro" => "Pacific/Majuro (MHT)",
            "Pacific/Midway" => "Pacific/Midway (SST)",
            "Pacific/Marquesas" => "Pacific/Marquesas (MART)",
            "Pacific/Nauru" => "Pacific/Nauru (NRT)",
            "Pacific/Niue" => "Pacific/Niue (NUT)",
            "Pacific/Norfolk" => "Pacific/Norfolk (NFT)",
            "Pacific/Noumea" => "Pacific/Noumea (NCT)",
            "Pacific/Pago_Pago" => "Pacific/Pago_Pago (SST)",
            "Pacific/Palau" => "Pacific/Palau (PWT)",
            "Pacific/Pitcairn" => "Pacific/Pitcairn (PST)",
            "Pacific/Pohnpei" => "Pacific/Pohnpei (PONT)",
            "Pacific/Port_Moresby" => "Pacific/Port_Moresby (PGT)",
            "Pacific/Rarotonga" => "Pacific/Rarotonga (CKT)",
            "Pacific/Saipan" => "Pacific/Saipan (ChST)",
            "Pacific/Tahiti" => "Pacific/Tahiti (TAHT)",
            "Pacific/Tarawa" => "Pacific/Tarawa (GILT)",
            "Pacific/Tongatapu" => "Pacific/Tongatapu (TOT)",
            "Pacific/Wake" => "Pacific/Wake (WAKT)",
            "Pacific/Wallis" => "Pacific/Wallis (WFT)"
        ];
    }

    public function listWithTime()
    {
        $result = [];
        foreach ($this->list() as $timezone => $label) {
            $result[$timezone] = (object)[
                'label' => $label,
                'time' => $this->timezoneToTime($timezone)
            ];
        }
        return $result;
    }

    public function timezoneToTime($timezone)
    {
        $tz = new DateTimeZone($timezone);
        $datetime = new \DateTime();
        $datetime->setTimezone($tz);
        $offset = $datetime->getOffset();
        $hours = floor(abs($offset) / 3600);
        $minutes = floor((abs($offset) % 3600) / 60);
        $sign = ($offset >= 0) ? '+' : '-';
        $hourOffset = sprintf("%02d", $hours);
        $minuteOffset = sprintf("%02d", $minutes);

        return $sign . $hourOffset . ':' . $minuteOffset;
    }
}
