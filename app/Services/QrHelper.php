<?php

namespace App\Services;

use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;

class QrHelper
{
    public function generate(string $target, string|null $logo_path = null, int $width = 512, int $height = 512)
    {
        $builderParams = [
            'writer' => new PngWriter(),
            'writerOptions' => [],
            'validateResult' => false,
            'data' => $target,
            'encoding' => new Encoding('UTF-8'),
            'errorCorrectionLevel' => ErrorCorrectionLevel::High,
            'size' => $width,
            'margin' => 10,
            'roundBlockSizeMode' => RoundBlockSizeMode::Margin,
        ];

        // Add logo if provided
        if (!empty($logo_path) && file_exists($logo_path)) {
            $builderParams['logoPath'] = $logo_path;
            $builderParams['logoResizeToWidth'] = intval($width / 3);
            $builderParams['logoPunchoutBackground'] = true;
        }

        $builder = new Builder(...$builderParams);
        $result = $builder->build();

        return base64_encode($result->getString());
    }
}
