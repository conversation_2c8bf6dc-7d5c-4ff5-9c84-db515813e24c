<?php

namespace App\Http\Controllers;

use App\Models\QrCode;
use App\Services\QrHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use function PHPUnit\Framework\directoryExists;

class QrGeneratorController extends Controller
{
    public function index()
    {
        $qr_list = QrCode::get();
        return view('qr_generator.index', compact('qr_list'));
    }

    public function create()
    {
        return view('qr_generator.create');
    }

    public function generate(Request $request)
    {
        $request->validate([
            'type' => 'required',
            'value' => 'required',
            'width' => 'required|integer',
            'height' => 'required|integer',
        ]);

        switch ($request->get('type')) {
            case "vcard":
                parse_str($request->get('value'), $data);
                $value = "BEGIN:VCARD\r\nVERSION:3.0\r\n"
                    . "N:" . $data['last_name'] . ";" . $data['first_name'] . "\r\n"
                    . "FN:" . $data['first_name'] . " " . $data['last_name'] . "\r\n"
                    . "TEL;TYPE=home,voice:" . $data['telephone'] . "\r\n"
                    . "TEL;TYPE=cell,voice:" . $data['cellno'] . "\r\n"
                    . "TEL;TYPE=home,fax:" . $data['fax'] . "\r\n"
                    . "URL;TYPE=website:" . $data['website'] . "\r\n"
                    . "EMAIL;TYPE=internet,pref:" . $data['email'] . "\r\n"
                    . "REV:" . date('Ymd') . "\r\n"
                    . "END:VCARD";
                break;
            case "wifi":
                parse_str($request->get('value'), $data);
                $value = "WIFI:S:" . $data['wifi_name'] . ";T:" . $data['wifi_decode'] . ";P:" . $data['wifi_pass'] . ";;";
                break;
            default:
                $value = $request->get('value');
                break;
        }

        $logo_path = null;
        if (!empty($logo = $request->file('logo'))) {
            $logo_path = $logo->getRealPath();
        }

        $hash_code = uniqid();

        $qr = new QrHelper();
        $result = $qr->generate(
            $request->get('type') == 'link' ? route('qr_generator.redirect', ['hash' => $hash_code]) : $value,
            $logo_path,
            $request->get('width'),
            $request->get('height')
        );
        $link = null;
        if (!empty($result)) {
            if (!Storage::exists('public/qr_codes')) {
                Storage::makeDirectory('public/qr_codes');
            }
            $fileName = uniqid() . '.png';
            Storage::put('public/qr_codes/' . $fileName, base64_decode($result));
            $link = Storage::url('public/qr_codes/' . $fileName);

            $code = new QrCode();
            $code->hash_code = $hash_code;
            $code->target = $value;
            $code->url = $link;
            $code->type = $request->get('type');
            $code->save();
        }
        return response()->json([
            'base64' => $result,
            'link' => $link
        ]);
    }

    public function edit(Request $request)
    {
        $code = QrCode::where('id', $request->get('id'));
        if ($code->exists()) {
            $code->update([
                'target' => $request->get('new_target')
            ]);
        }
        return redirect()->route('qr_generator');
    }

    public function redirect($hash)
    {
        $qr_code = QrCode::where('hash_code', $hash)->first();
        return redirect()->to($qr_code->target);
    }
}
