<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON to Excel Test</title>
    <script src="http://localhost:8000/js/libs/xlsx.full.min.js"></script>
</head>
<body>
    <h1>JSON to Excel Test</h1>
    <button onclick="testConversion()">Test JSON to Excel Conversion</button>
    <div id="result"></div>

    <script>
        function testConversion() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Check if XLSX is available
                if (typeof XLSX === 'undefined') {
                    resultDiv.innerHTML = '<p style="color: red;">❌ XLSX library is not loaded</p>';
                    return;
                }
                
                resultDiv.innerHTML = '<p style="color: green;">✅ XLSX library is loaded (version: ' + (XLSX.version || 'unknown') + ')</p>';
                
                // Test data
                const testData = [
                    { name: '<PERSON>', age: 30, city: 'New York' },
                    { name: '<PERSON>', age: 25, city: 'Los Angeles' },
                    { name: '<PERSON>', age: 35, city: 'Chicago' }
                ];
                
                // Create workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(testData);
                XLSX.utils.book_append_sheet(wb, ws, "TestData");
                
                // Generate file
                XLSX.writeFile(wb, 'test-export.xlsx');
                
                resultDiv.innerHTML += '<p style="color: green;">✅ Excel file generated successfully!</p>';
                
            } catch (error) {
                resultDiv.innerHTML += '<p style="color: red;">❌ Error: ' + error.message + '</p>';
                console.error('Test error:', error);
            }
        }
    </script>
</body>
</html>
