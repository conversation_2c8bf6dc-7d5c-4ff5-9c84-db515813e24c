/*!
 RowGroup 1.1.3
 ©2017-2021 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(f){return c(f,window,document)}):"object"===typeof exports?module.exports=function(f,i){f||(f=window);if(!i||!i.fn.dataTable)i=require("datatables.net")(f,i).$;return c(i,f,f.document)}:c(jQuery,window,document)})(function(c,f,i,k){var d=c.fn.dataTable,g=function(a,b){if(!d.versionCheck||!d.versionCheck("1.10.8"))throw"RowGroup requires DataTables 1.10.8 or newer";this.c=c.extend(!0,{},d.defaults.rowGroup,
g.defaults,b);this.s={dt:new d.Api(a)};this.dom={};var m=this.s.dt.settings()[0],e=m.rowGroup;if(e)return e;m.rowGroup=this;this._constructor()};c.extend(g.prototype,{dataSrc:function(a){if(a===k)return this.c.dataSrc;var b=this.s.dt;this.c.dataSrc=a;c(b.table().node()).triggerHandler("rowgroup-datasrc.dt",[b,a]);return this},disable:function(){this.c.enable=!1;return this},enable:function(a){if(!1===a)return this.disable();this.c.enable=!0;return this},enabled:function(){return this.c.enable},_constructor:function(){var a=
this,b=this.s.dt,m=b.settings()[0];b.on("draw.dtrg",function(b,c){a.c.enable&&m===c&&a._draw()});b.on("column-visibility.dt.dtrg responsive-resize.dt.dtrg",function(){a._adjustColspan()});b.on("destroy",function(){b.off(".dtrg")})},_adjustColspan:function(){c("tr."+this.c.className,this.s.dt.table().body()).find("td:visible").attr("colspan",this._colspan())},_colspan:function(){return this.s.dt.columns().visible().reduce(function(a,b){return a+b},0)},_draw:function(){var a=this._group(0,this.s.dt.rows({page:"current"}).indexes());
this._groupDisplay(0,a)},_group:function(a,b){for(var c=Array.isArray(this.c.dataSrc)?this.c.dataSrc:[this.c.dataSrc],e=d.ext.oApi._fnGetObjectDataFn(c[a]),f=this.s.dt,j,g,l=[],h=0,i=b.length;h<i;h++){var n=b[h];j=f.row(n).data();j=e(j);if(null===j||j===k)j=this.c.emptyDataGroup;if(g===k||j!==g)l.push({dataPoint:j,rows:[]}),g=j;l[l.length-1].rows.push(n)}if(c[a+1]!==k){h=0;for(i=l.length;h<i;h++)l[h].children=this._group(a+1,l[h].rows)}return l},_groupDisplay:function(a,b){for(var c=this.s.dt,e,g=
0,i=b.length;g<i;g++){var d=b[g],f=d.dataPoint,h=d.rows;this.c.startRender&&(e=this.c.startRender.call(this,c.rows(h),f,a),(e=this._rowWrap(e,this.c.startClassName,a))&&e.insertBefore(c.row(h[0]).node()));this.c.endRender&&(e=this.c.endRender.call(this,c.rows(h),f,a),(e=this._rowWrap(e,this.c.endClassName,a))&&e.insertAfter(c.row(h[h.length-1]).node()));d.children&&this._groupDisplay(a+1,d.children)}},_rowWrap:function(a,b,d){if(null===a||""===a)a=this.c.emptyDataGroup;return a===k||null===a?null:
("object"===typeof a&&a.nodeName&&"tr"===a.nodeName.toLowerCase()?c(a):a instanceof c&&a.length&&"tr"===a[0].nodeName.toLowerCase()?a:c("<tr/>").append(c("<td/>").attr("colspan",this._colspan()).append(a))).addClass(this.c.className).addClass(b).addClass("dtrg-level-"+d)}});g.defaults={className:"dtrg-group",dataSrc:0,emptyDataGroup:"No group",enable:!0,endClassName:"dtrg-end",endRender:null,startClassName:"dtrg-start",startRender:function(a,b){return b}};g.version="1.1.3";c.fn.dataTable.RowGroup=
g;c.fn.DataTable.RowGroup=g;d.Api.register("rowGroup()",function(){return this});d.Api.register("rowGroup().disable()",function(){return this.iterator("table",function(a){a.rowGroup&&a.rowGroup.enable(!1)})});d.Api.register("rowGroup().enable()",function(a){return this.iterator("table",function(b){b.rowGroup&&b.rowGroup.enable(a===k?!0:a)})});d.Api.register("rowGroup().enabled()",function(){var a=this.context;return a.length&&a[0].rowGroup?a[0].rowGroup.enabled():!1});d.Api.register("rowGroup().dataSrc()",
function(a){return a===k?this.context[0].rowGroup.dataSrc():this.iterator("table",function(b){b.rowGroup&&b.rowGroup.dataSrc(a)})});c(i).on("preInit.dt.dtrg",function(a,b){if("dt"===a.namespace){var f=b.oInit.rowGroup,e=d.defaults.rowGroup;if(f||e)e=c.extend({},e,f),!1!==f&&new g(b,e)}});return g});
